# selenium_agent_tool.py
from agents import function_tool  # OpenAI Agent SDK decorator
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from urllib.parse import quote_plus

# helper functions
def _choose_link_element(item):
    for sel in ("h3 a", "h2 a", "a.v-align-middle", "a[href*='github.com/']"):
        try:
            return item.find_element(By.CSS_SELECTOR, sel)
        except:
            continue
    try:
        return item.find_element(By.TAG_NAME, "a")
    except:
        return None

# main tool
@function_tool(name="selenium_live_data", description="Search GitHub or trending repos and return top results as JSON")
def selenium_live_data_tool(query: str = None, max_results: int = 5, headless: bool = True):
    """
    Fetch top GitHub repos for a query or trending repos if no query is provided.
    Returns list of dict: {name, url, description, stars, language}
    """
    chrome_options = Options()
    if headless:
        try:
            chrome_options.add_argument("--headless=new")
        except:
            chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
    results = []

    try:
        url = f"https://github.com/search?q={quote_plus(query)}&type=repositories" if query else "https://github.com/trending"
        driver.get(url)
        wait = WebDriverWait(driver, 10)
        try:
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "ul.repo-list li, article.Box-row")))
        except:
            pass

        items = driver.find_elements(By.CSS_SELECTOR, "ul.repo-list li")
        if not items:
            items = driver.find_elements(By.CSS_SELECTOR, "article.Box-row")

        for item in items[:max_results]:
            link = _choose_link_element(item)
            repo_name = link.text.strip() if link else ""
            repo_url = link.get_attribute("href") if link else ""
            # description
            try:
                description = item.find_element(By.CSS_SELECTOR, "p").text.strip()
            except:
                description = ""
            # stars
            stars = ""
            try:
                stars_el = item.find_element(By.CSS_SELECTOR, "a[href*='stargazers']")
                stars = stars_el.text.strip()
            except:
                stars = ""
            # language
            language = ""
            try:
                language = item.find_element(By.CSS_SELECTOR, "[itemprop='programmingLanguage']").text.strip()
            except:
                language = ""

            results.append({
                "name": repo_name,
                "url": repo_url,
                "description": description,
                "stars": stars,
                "language": language
            })

    finally:
        driver.quit()

    return results
