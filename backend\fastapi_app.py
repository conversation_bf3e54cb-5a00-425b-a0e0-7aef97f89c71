import os
import asyncio
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from my_agent import agent   # agent import from your file
from config import run_config
from agents import Runner

load_dotenv()

app = FastAPI()

# CORS setup
origins = [os.getenv("CORS_ORIGINS", "http://localhost:3000")]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/agent")
async def agent_route(request: Request):
    data = await request.json()
    user_msg = data.get("message", "").strip()
    if not user_msg:
        return {"error": "message is required"}

    try:
        result = await Runner.run(
            starting_agent=agent, 
            input=user_msg, 
            run_config=run_config
        )
        return {"reply": result.final_output}
    except Exception as e:
        return {"error": str(e)}

@app.get("/health")
async def health():
    return {"status": "ok"}

# Run with: run_backend.bat run this command in cmd then your fastapi server will be run
